<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="x-apple-disable-message-reformatting">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no">
    <style type="text/css">
        body, table, td {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        }
        .ExternalClass { width: 100%; }
        .ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div {
            line-height: 150%;
        }
        a { text-decoration: none; }
        * { color: inherit; }
        a[x-apple-data-detectors], u+#body a, #MessageViewBody a {
            color: inherit;
            text-decoration: none;
            font-size: inherit;
            font-family: inherit;
            font-weight: inherit;
            line-height: inherit;
        }
        img { -ms-interpolation-mode: bicubic; }
        table:not([class^=s-]) {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
            border-spacing: 0px;
            border-collapse: collapse;
        }
        table:not([class^=s-]) td {
            border-spacing: 0px;
            border-collapse: collapse;
        }

        /* Custom styles for better appearance */
        .main-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
        }

        .header-section {
            background: linear-gradient(135deg, #e32652 0%, #c41e3a 100%);
            border-radius: 12px 12px 0 0;
            padding: 30px 40px 20px 40px;
        }

        .content-section {
            padding: 30px 40px;
        }

        .applicant-highlight {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            border-left: 4px solid #10b981;
            padding: 20px 24px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }

        .applicant-details {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .detail-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e2e8f0;
        }

        .detail-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }

        .detail-label {
            font-weight: 600;
            color: #4a5568;
            width: 100px;
            flex-shrink: 0;
            margin-right: 15px;
        }

        .detail-value {
            color: #1a202c;
            font-weight: 500;
        }

        .cta-button {
            background: linear-gradient(135deg, #e32652 0%, #c41e3a 100%);
            color: white !important;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            box-shadow: 0 4px 12px rgba(227, 38, 82, 0.3);
            transition: all 0.3s ease;
        }

        .cta-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(227, 38, 82, 0.4);
        }

        .user-icon {
            background: #e32652;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }

        .info-box {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .process-steps {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }

        @media screen and (max-width: 600px) {
            .header-section, .content-section {
                padding: 20px !important;
            }
            .applicant-highlight, .applicant-details, .process-steps {
                margin: 15px 0;
                padding: 16px 20px;
            }
            .detail-item {
                flex-direction: column;
                align-items: flex-start;
            }
            .detail-label {
                width: 100%;
                margin-bottom: 5px;
                margin-right: 0;
            }
            .cta-button {
                padding: 14px 24px;
                font-size: 15px;
            }
        }
    </style>
</head>
<body style="margin: 0; padding: 0; background-color: #f1f5f9; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
<table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #f1f5f9;">
    <tr>
        <td align="center" style="padding: 40px 20px;">

            <!-- Main Email Container -->
            <table class="main-card" role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px; margin: 0 auto;">

                <!-- Header Section -->
                <tr>
                    <td class="header-section" align="center">
                        <div class="user-icon">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z" fill="white"/>
                            </svg>
                        </div>
                        <h1 style="color: white; font-size: 28px; font-weight: 700; margin: 0; line-height: 1.3;">
                            Neue Bewerbung eingegangen! 🎉
                        </h1>
                        <p style="color: rgba(255, 255, 255, 0.9); font-size: 16px; margin: 10px 0 0 0; line-height: 1.5;">
                            Ein neuer Kandidat interessiert sich für Ihre Stelle
                        </p>
                    </td>
                </tr>

                <!-- Content Section -->
                <tr>
                    <td class="content-section">

                        <!-- Introduction -->
                        <p style="color: #4a5568; font-size: 16px; line-height: 1.6; margin: 0 0 25px 0;">
                            <strong>Großartige Neuigkeiten!</strong> Sie haben eine neue Bewerbung für eine Ihrer Stellenanzeigen erhalten.
                            Ein qualifizierter Kandidat zeigt Interesse an Ihrem Unternehmen.
                        </p>

                        <!-- Applicant Information Highlight -->
                        <div class="applicant-highlight">
                            <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <td>
                                        <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" style="margin-right: 12px;">
                                                <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V5H19V19ZM7 7H17V9H7V7ZM7 11H17V13H7V11ZM7 15H13V17H7V15Z" fill="#10b981"/>
                                            </svg>
                                            <h3 style="color: #065f46; font-size: 18px; font-weight: 600; margin: 0;">
                                                Bewerbungsdetails
                                            </h3>
                                        </div>

                                        <div class="applicant-details">
                                            <div class="detail-item">
                                                <span class="detail-label">💼 Stelle:</span>
                                                <span class="detail-value">{{jobAdTitle}}</span>
                                            </div>
                                            <div class="detail-item">
                                                <span class="detail-label">👤 Bewerber:</span>
                                                <span class="detail-value">{{applicantName}}</span>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <!-- Process Information -->
                        <div class="process-steps">
                            <h4 style="color: #0c4a6e; font-size: 16px; font-weight: 600; margin: 0 0 15px 0;">
                                🚀 Nächste Schritte im Bewerbungsprozess:
                            </h4>
                            <p style="color: #1e40af; font-size: 14px; line-height: 1.6; margin: 0;">
                                Über das Dashboard können Sie sich die Details des Bewerbers ansehen und diesen bei Interesse matchen.
                                Daraufhin können Sie direkt mit dem Bewerber per Chat oder Videocall kommunizieren.
                            </p>
                        </div>

                        <!-- Benefits Section -->
                        <div class="info-box">
                            <h4 style="color: #1a202c; font-size: 16px; font-weight: 600; margin: 0 0 15px 0;">
                                ✨ Ihre Vorteile:
                            </h4>
                            <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <td style="padding: 4px 0;">
                                        <div style="display: flex; align-items: center;">
                                            <span style="color: #10b981; font-size: 16px; margin-right: 12px;">✓</span>
                                            <span style="color: #4a5568; font-size: 14px;">Direkter Kontakt zum Bewerber ohne Umwege</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="padding: 4px 0;">
                                        <div style="display: flex; align-items: center;">
                                            <span style="color: #10b981; font-size: 16px; margin-right: 12px;">✓</span>
                                            <span style="color: #4a5568; font-size: 14px;">Integrierte Chat- und Videocall-Funktionen</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="padding: 4px 0;">
                                        <div style="display: flex; align-items: center;">
                                            <span style="color: #10b981; font-size: 16px; margin-right: 12px;">✓</span>
                                            <span style="color: #4a5568; font-size: 14px;">Einfaches Matching-System für qualifizierte Kandidaten</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="padding: 4px 0;">
                                        <div style="display: flex; align-items: center;">
                                            <span style="color: #10b981; font-size: 16px; margin-right: 12px;">✓</span>
                                            <span style="color: #4a5568; font-size: 14px;">Übersichtliche Verwaltung aller Bewerbungen</span>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <!-- Call to Action -->
                        <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="margin: 35px 0 25px 0;">
                            <tr>
                                <td align="center">
                                    <a href="https://dashboard.bridge-app.de/jobAds/{{jobAdId}}" class="cta-button" style="background: linear-gradient(135deg, #e32652 0%, #c41e3a 100%); color: white !important; padding: 16px 32px; border-radius: 8px; font-weight: 600; font-size: 16px; text-decoration: none; display: inline-block; box-shadow: 0 4px 12px rgba(227, 38, 82, 0.3);">
                                        🔍 Bewerbung jetzt ansehen
                                    </a>
                                </td>
                            </tr>
                        </table>

                        <!-- Important Note -->
                        <div style="background: #fffbeb; border: 1px solid #fbbf24; border-radius: 8px; padding: 16px; margin: 25px 0;">
                            <p style="color: #92400e; font-size: 14px; margin: 0; line-height: 1.5;">
                                <strong>⏰ Tipp:</strong> Schnelle Reaktionen auf Bewerbungen erhöhen Ihre Chancen auf erfolgreiche Vermittlungen erheblich.
                                Die besten Kandidaten sind oft sehr gefragt!
                            </p>
                        </div>

                    </td>
                </tr>
            </table>

            <!-- Footer -->
            <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="margin-top: 40px;">
                <tr>
                    <td align="center">
                        <img src="https://firebasestorage.googleapis.com/v0/b/bridge-public-v2/o/dist%2Fimages%2Fbridge-logo-fae1080e.png?alt=media&token=a0a04c7d-33b4-44bb-b97a-fd1a96a493e3"
                             alt="Bridge Logo"
                             width="120"
                             style="display: block; margin-bottom: 20px; opacity: 0.8;">
                    </td>
                </tr>
                <tr>
                    <td align="center">
                        <p style="color: #718096; font-size: 14px; line-height: 1.5; margin: 0;">
                            <strong>Berufe4you UG</strong><br>
                            Logistikpark 7<br>
                            95448 Bayreuth<br><br>
                            <a href="mailto:<EMAIL>" style="color: #e32652; text-decoration: none;"><EMAIL></a>
                        </p>
                    </td>
                </tr>
            </table>

        </td>
    </tr>
</table>
</body>
</html>