<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="x-apple-disable-message-reformatting">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no">
    <style type="text/css">
        body, table, td {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        }
        .ExternalClass { width: 100%; }
        .ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div {
            line-height: 150%;
        }
        a { text-decoration: none; }
        * { color: inherit; }
        a[x-apple-data-detectors], u+#body a, #MessageViewBody a {
            color: inherit;
            text-decoration: none;
            font-size: inherit;
            font-family: inherit;
            font-weight: inherit;
            line-height: inherit;
        }
        img { -ms-interpolation-mode: bicubic; }
        table:not([class^=s-]) {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
            border-spacing: 0px;
            border-collapse: collapse;
        }
        table:not([class^=s-]) td {
            border-spacing: 0px;
            border-collapse: collapse;
        }

        /* Custom styles for better appearance */
        .main-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
        }

        .header-section {
            background: linear-gradient(135deg, #e32652 0%, #c41e3a 100%);
            border-radius: 12px 12px 0 0;
            padding: 30px 40px 20px 40px;
        }

        .content-section {
            padding: 30px 40px;
        }

        .job-highlight {
            background: #f8fafc;
            border-left: 4px solid #e32652;
            padding: 16px 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }

        .cta-button {
            background: linear-gradient(135deg, #e32652 0%, #c41e3a 100%);
            color: white !important;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            box-shadow: 0 4px 12px rgba(227, 38, 82, 0.3);
            transition: all 0.3s ease;
        }

        .cta-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(227, 38, 82, 0.4);
        }

        .message-icon {
            background: #e32652;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }

        @media screen and (max-width: 600px) {
            .header-section, .content-section {
                padding: 20px !important;
            }
            .job-highlight {
                margin: 15px 0;
                padding: 12px 16px;
            }
            .cta-button {
                padding: 14px 24px;
                font-size: 15px;
            }
        }
    </style>
</head>
<body style="margin: 0; padding: 0; background-color: #f1f5f9; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
<table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #f1f5f9;">
    <tr>
        <td align="center" style="padding: 40px 20px;">

            <!-- Main Email Container -->
            <table class="main-card" role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px; margin: 0 auto;">

                <!-- Header Section -->
                <tr>
                    <td class="header-section" align="center">
                        <div class="message-icon">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 4H4C2.9 4 2.01 4.9 2.01 6L2 18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4ZM20 8L12 13L4 8V6L12 11L20 6V8Z" fill="white"/>
                            </svg>
                        </div>
                        <h1 style="color: white; font-size: 28px; font-weight: 700; margin: 0; line-height: 1.3;">
                            Neue Chat-Nachricht erhalten! 💬
                        </h1>
                        <p style="color: rgba(255, 255, 255, 0.9); font-size: 16px; margin: 10px 0 0 0; line-height: 1.5;">
                            Eine wichtige Nachricht wartet auf Sie
                        </p>
                    </td>
                </tr>

                <!-- Content Section -->
                <tr>
                    <td class="content-section">

                        <!-- Job Information Highlight -->
                        <div class="job-highlight">
                            <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <td>
                                        <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" style="margin-right: 8px;">
                                                <path d="M12 7V3H2V21H22V7H12ZM6 19H4V17H6V19ZM6 15H4V13H6V15ZM6 11H4V9H6V11ZM6 7H4V5H6V7ZM10 19H8V17H10V19ZM10 15H8V13H10V15ZM10 11H8V9H10V11ZM10 7H8V5H10V7ZM20 19H12V17H20V19ZM20 15H12V13H20V15ZM20 11H12V9H20V11Z" fill="#e32652"/>
                                            </svg>
                                            <strong style="color: #1a202c; font-size: 16px;">Stellenausschreibung</strong>
                                        </div>
                                        <p style="color: #4a5568; font-size: 18px; font-weight: 600; margin: 0; line-height: 1.4;">
                                            {{jobAdTitle}}
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <!-- Main Message -->
                        <p style="color: #4a5568; font-size: 16px; line-height: 1.6; margin: 25px 0;">
                            <strong>Gute Neuigkeiten!</strong> Es ist eine neue Nachricht zu Ihrer Stellenausschreibung eingegangen.
                            Öffnen Sie die Bridge-App, um die Nachricht zu lesen und direkt zu antworten.
                        </p>

                        <!-- Features List -->
                        <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="margin: 25px 0;">
                            <tr>
                                <td style="padding: 8px 0;">
                                    <div style="display: flex; align-items: center;">
                                        <span style="color: #10b981; font-size: 18px; margin-right: 12px;">✓</span>
                                        <span style="color: #4a5568; font-size: 14px;">Sofortige Benachrichtigung über neue Nachrichten</span>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0;">
                                    <div style="display: flex; align-items: center;">
                                        <span style="color: #10b981; font-size: 18px; margin-right: 12px;">✓</span>
                                        <span style="color: #4a5568; font-size: 14px;">Direkter Zugang zu Ihrem Dashboard</span>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0;">
                                    <div style="display: flex; align-items: center;">
                                        <span style="color: #10b981; font-size: 18px; margin-right: 12px;">✓</span>
                                        <span style="color: #4a5568; font-size: 14px;">Einfache Kommunikation mit Bewerbern</span>
                                    </div>
                                </td>
                            </tr>
                        </table>

                        <!-- Call to Action -->
                        <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="margin: 35px 0 25px 0;">
                            <tr>
                                <td align="center">
                                    <a href="https://dashboard.bridge-app.de" class="cta-button" style="background: linear-gradient(135deg, #e32652 0%, #c41e3a 100%); color: white !important; padding: 16px 32px; border-radius: 8px; font-weight: 600; font-size: 16px; text-decoration: none; display: inline-block; box-shadow: 0 4px 12px rgba(227, 38, 82, 0.3);">
                                        🚀 Jetzt Nachricht lesen
                                    </a>
                                </td>
                            </tr>
                        </table>

                        <!-- Additional Help -->
                        <div style="background: #f8fafc; border-radius: 8px; padding: 20px; margin: 25px 0; border: 1px solid #e2e8f0;">
                            <p style="color: #4a5568; font-size: 14px; margin: 0; line-height: 1.5;">
                                <strong>💡 Tipp:</strong> Schnelle Antworten erhöhen Ihre Chancen auf erfolgreiche Vermittlungen.
                                Bewerber schätzen prompte Rückmeldungen sehr!
                            </p>
                        </div>

                    </td>
                </tr>
            </table>

            <!-- Footer -->
            <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="margin-top: 40px;">
                <tr>
                    <td align="center">
                        <img src="https://firebasestorage.googleapis.com/v0/b/bridge-public-v2/o/dist%2Fimages%2Fbridge-logo-fae1080e.png?alt=media&token=a0a04c7d-33b4-44bb-b97a-fd1a96a493e3"
                             alt="Bridge Logo"
                             width="120"
                             style="display: block; margin-bottom: 20px; opacity: 0.8;">
                    </td>
                </tr>
                <tr>
                    <td align="center">
                        <p style="color: #718096; font-size: 14px; line-height: 1.5; margin: 0;">
                            <strong>Berufe4you UG</strong><br>
                            Logistikpark 7<br>
                            95448 Bayreuth<br><br>
                            <a href="mailto:<EMAIL>" style="color: #e32652; text-decoration: none;"><EMAIL></a>
                        </p>
                    </td>
                </tr>
            </table>

        </td>
    </tr>
</table>
</body>
</html>