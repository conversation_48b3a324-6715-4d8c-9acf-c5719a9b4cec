<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="x-apple-disable-message-reformatting">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no">
    <style type="text/css">
        body, table, td {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        }
        .ExternalClass { width: 100%; }
        .ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div {
            line-height: 150%;
        }
        a { text-decoration: none; }
        * { color: inherit; }
        a[x-apple-data-detectors], u+#body a, #MessageViewBody a {
            color: inherit;
            text-decoration: none;
            font-size: inherit;
            font-family: inherit;
            font-weight: inherit;
            line-height: inherit;
        }
        img { -ms-interpolation-mode: bicubic; }
        table:not([class^=s-]) {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
            border-spacing: 0px;
            border-collapse: collapse;
        }
        table:not([class^=s-]) td {
            border-spacing: 0px;
            border-collapse: collapse;
        }

        /* Custom styles for better appearance */
        .main-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
        }

        .header-section {
            background: linear-gradient(135deg, #e32652 0%, #c41e3a 100%);
            border-radius: 12px 12px 0 0;
            padding: 30px 40px 20px 40px;
        }

        .content-section {
            padding: 30px 40px;
        }

        .job-highlight {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-left: 4px solid #0ea5e9;
            padding: 20px 24px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }

        .cta-button {
            background: linear-gradient(135deg, #e32652 0%, #c41e3a 100%);
            color: white !important;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            box-shadow: 0 4px 12px rgba(227, 38, 82, 0.3);
            transition: all 0.3s ease;
        }

        .cta-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(227, 38, 82, 0.4);
        }

        .briefcase-icon {
            background: #e32652;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }

        .info-box {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        @media screen and (max-width: 600px) {
            .header-section, .content-section {
                padding: 20px !important;
            }
            .job-highlight {
                margin: 15px 0;
                padding: 16px 20px;
            }
            .cta-button {
                padding: 14px 24px;
                font-size: 15px;
            }
            .info-box {
                padding: 16px;
                margin: 15px 0;
            }
        }
    </style>
</head>
<body style="margin: 0; padding: 0; background-color: #f1f5f9; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
<table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #f1f5f9;">
    <tr>
        <td align="center" style="padding: 40px 20px;">

            <!-- Main Email Container -->
            <table class="main-card" role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px; margin: 0 auto;">

                <!-- Header Section -->
                <tr>
                    <td class="header-section" align="center">
                        <div class="briefcase-icon">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M10 16V8C10 6.9 10.9 6 12 6S14 6.9 14 8V16C14 17.1 13.1 18 12 18S10 17.1 10 16ZM14 2H10C8.9 2 8 2.9 8 4V6H20C21.1 6 22 6.9 22 8V19C22 20.1 21.1 21 20 21H4C2.9 21 2 20.1 2 19V8C2 6.9 2.9 6 4 6H16V4C16 2.9 15.1 2 14 2Z" fill="white"/>
                            </svg>
                        </div>
                        <h1 style="color: white; font-size: 28px; font-weight: 700; margin: 0; line-height: 1.3;">
                            Neue Stellenanzeige verfügbar! 🎯
                        </h1>
                        <p style="color: rgba(255, 255, 255, 0.9); font-size: 16px; margin: 10px 0 0 0; line-height: 1.5;">
                            Eine passende Position wartet auf Sie
                        </p>
                    </td>
                </tr>

                <!-- Content Section -->
                <tr>
                    <td class="content-section">

                        <!-- Introduction -->
                        <p style="color: #4a5568; font-size: 16px; line-height: 1.6; margin: 0 0 25px 0;">
                            <strong>Großartige Neuigkeiten!</strong> Eine neue Stellenanzeige wurde in Ihrem System erstellt und wartet auf Ihre Aufmerksamkeit.
                        </p>

                        <!-- Job Information Highlight -->
                        <div class="job-highlight">
                            <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <td>
                                        <div style="display: flex; align-items: center; margin-bottom: 12px;">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" style="margin-right: 12px;">
                                                <path d="M21 6H3C2.45 6 2 6.45 2 7V17C2 17.55 2.45 18 3 18H21C21.55 18 22 17.55 22 17V7C22 6.45 21.55 6 21 6ZM20 16H4V8H20V16ZM6 10H8V14H6V10ZM10 10H18V12H10V10ZM10 13H16V14H10V13Z" fill="#0ea5e9"/>
                                            </svg>
                                            <h3 style="color: #0c4a6e; font-size: 18px; font-weight: 600; margin: 0;">
                                                Stellenausschreibung
                                            </h3>
                                        </div>
                                        <p style="color: #1e40af; font-size: 20px; font-weight: 700; margin: 0; line-height: 1.4;">
                                            {{jobAdTitle}}
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <!-- Key Benefits -->
                        <div class="info-box">
                            <h4 style="color: #1a202c; font-size: 16px; font-weight: 600; margin: 0 0 15px 0;">
                                📈 Vorteile der Bridge-Plattform:
                            </h4>
                            <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <td style="padding: 4px 0;">
                                        <div style="display: flex; align-items: center;">
                                            <span style="color: #10b981; font-size: 16px; margin-right: 12px;">✓</span>
                                            <span style="color: #4a5568; font-size: 14px;">Direkter Zugang zu qualifizierten Bewerbern</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="padding: 4px 0;">
                                        <div style="display: flex; align-items: center;">
                                            <span style="color: #10b981; font-size: 16px; margin-right: 12px;">✓</span>
                                            <span style="color: #4a5568; font-size: 14px;">Einfache Verwaltung Ihrer Stellenanzeigen</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="padding: 4px 0;">
                                        <div style="display: flex; align-items: center;">
                                            <span style="color: #10b981; font-size: 16px; margin-right: 12px;">✓</span>
                                            <span style="color: #4a5568; font-size: 14px;">Integrierte Kommunikation mit Kandidaten</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="padding: 4px 0;">
                                        <div style="display: flex; align-items: center;">
                                            <span style="color: #10b981; font-size: 16px; margin-right: 12px;">✓</span>
                                            <span style="color: #4a5568; font-size: 14px;">Automatisierte Terminvereinbarungen</span>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <!-- Call to Action -->
                        <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="margin: 35px 0 25px 0;">
                            <tr>
                                <td align="center">
                                    <a href="https://dashboard.bridge-app.de/company/{{companyId}}/job-ads/{{jobAdvertId}}" class="cta-button" style="background: linear-gradient(135deg, #e32652 0%, #c41e3a 100%); color: white !important; padding: 16px 32px; border-radius: 8px; font-weight: 600; font-size: 16px; text-decoration: none; display: inline-block; box-shadow: 0 4px 12px rgba(227, 38, 82, 0.3);">
                                        🚀 Stellenanzeige verwalten
                                    </a>
                                </td>
                            </tr>
                        </table>

                        <!-- Next Steps -->
                        <div style="background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; padding: 20px; margin: 25px 0;">
                            <h4 style="color: #0c4a6e; font-size: 16px; font-weight: 600; margin: 0 0 12px 0;">
                                📋 Nächste Schritte:
                            </h4>
                            <ul style="color: #1e40af; font-size: 14px; line-height: 1.6; margin: 0; padding-left: 20px;">
                                <li>Überprüfen Sie die Details Ihrer Stellenanzeige</li>
                                <li>Optimieren Sie bei Bedarf die Beschreibung</li>
                                <li>Überwachen Sie eingehende Bewerbungen</li>
                                <li>Nutzen Sie die Chat-Funktion für direkte Kommunikation</li>
                            </ul>
                        </div>

                        <!-- Important Note -->
                        <div style="background: #fffbeb; border: 1px solid #fbbf24; border-radius: 8px; padding: 16px; margin: 25px 0;">
                            <p style="color: #92400e; font-size: 14px; margin: 0; line-height: 1.5;">
                                <strong>💡 Tipp:</strong> Aktive Stellenanzeigen mit vollständigen Beschreibungen und schnellen Antworten erhalten 3x mehr qualifizierte Bewerbungen!
                            </p>
                        </div>

                    </td>
                </tr>
            </table>

            <!-- Footer -->
            <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="margin-top: 40px;">
                <tr>
                    <td align="center">
                        <img src="https://firebasestorage.googleapis.com/v0/b/bridge-public-v2/o/dist%2Fimages%2Fbridge-logo-fae1080e.png?alt=media&token=a0a04c7d-33b4-44bb-b97a-fd1a96a493e3"
                             alt="Bridge Logo"
                             width="120"
                             style="display: block; margin-bottom: 20px; opacity: 0.8;">
                    </td>
                </tr>
                <tr>
                    <td align="center">
                        <p style="color: #718096; font-size: 14px; line-height: 1.5; margin: 0;">
                            <strong>Berufe4you UG</strong><br>
                            Logistikpark 7<br>
                            95448 Bayreuth<br><br>
                            <a href="mailto:<EMAIL>" style="color: #e32652; text-decoration: none;"><EMAIL></a>
                        </p>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>
</body>
</html>