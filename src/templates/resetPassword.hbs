<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="x-apple-disable-message-reformatting">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no">
    <title>{{ title }}</title>
    <style type="text/css">
        body, table, td {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        }
        .ExternalClass { width: 100%; }
        .ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div {
            line-height: 150%;
        }
        a { text-decoration: none; }
        * { color: inherit; }
        a[x-apple-data-detectors], u+#body a, #MessageViewBody a {
            color: inherit;
            text-decoration: none;
            font-size: inherit;
            font-family: inherit;
            font-weight: inherit;
            line-height: inherit;
        }
        img { -ms-interpolation-mode: bicubic; }
        table:not([class^=s-]) {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
            border-spacing: 0px;
            border-collapse: collapse;
        }
        table:not([class^=s-]) td {
            border-spacing: 0px;
            border-collapse: collapse;
        }

        /* Custom styles for better appearance */
        .preheader {
            color: transparent;
            display: none;
            height: 0;
            max-height: 0;
            max-width: 0;
            opacity: 0;
            overflow: hidden;
            mso-hide: all;
            visibility: hidden;
            width: 0;
        }

        .main-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
        }

        .header-section {
            background: linear-gradient(135deg, #e32652 0%, #c41e3a 100%);
            border-radius: 12px 12px 0 0;
            padding: 30px 40px 20px 40px;
        }

        .content-section {
            padding: 30px 40px;
        }

        .security-highlight {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-left: 4px solid #f59e0b;
            padding: 20px 24px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }

        .cta-button {
            background: linear-gradient(135deg, #e32652 0%, #c41e3a 100%);
            color: white !important;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            box-shadow: 0 4px 12px rgba(227, 38, 82, 0.3);
            transition: all 0.3s ease;
        }

        .cta-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(227, 38, 82, 0.4);
        }

        .lock-icon {
            background: #e32652;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }

        .info-box {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .security-tips {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }

        @media screen and (max-width: 600px) {
            .header-section, .content-section {
                padding: 20px !important;
            }
            .security-highlight, .security-tips {
                margin: 15px 0;
                padding: 16px 20px;
            }
            .cta-button {
                padding: 14px 24px;
                font-size: 15px;
                width: 100%;
                text-align: center;
            }
            .info-box {
                padding: 16px;
                margin: 15px 0;
            }
        }
    </style>
</head>
<body style="margin: 0; padding: 0; background-color: #f1f5f9; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
<span class="preheader">Bitte setzen Sie Ihr Passwort zurück.</span>
<table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #f1f5f9;">
    <tr>
        <td align="center" style="padding: 40px 20px;">

            <!-- Main Email Container -->
            <table class="main-card" role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px; margin: 0 auto;">

                <!-- Header Section -->
                <tr>
                    <td class="header-section" align="center">
                        <div class="lock-icon">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M18 8H17V6C17 3.24 14.76 1 12 1S7 3.24 7 6V8H6C4.9 8 4 8.9 4 10V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V10C20 8.9 19.1 8 18 8ZM12 17C10.9 17 10 16.1 10 15S10.9 13 12 13S14 13.9 14 15S13.1 17 12 17ZM15.1 8H8.9V6C8.9 4.29 10.29 2.9 12 2.9S15.1 4.29 15.1 6V8Z" fill="white"/>
                            </svg>
                        </div>
                        <h1 style="color: white; font-size: 28px; font-weight: 700; margin: 0; line-height: 1.3;">
                            Passwort zurücksetzen 🔐
                        </h1>
                        <p style="color: rgba(255, 255, 255, 0.9); font-size: 16px; margin: 10px 0 0 0; line-height: 1.5;">
                            Sicherer Zugang zu Ihrem Bridge-Account
                        </p>
                    </td>
                </tr>

                <!-- Content Section -->
                <tr>
                    <td class="content-section">

                        <!-- Greeting -->
                        <p style="color: #4a5568; font-size: 18px; line-height: 1.6; margin: 0 0 25px 0;">
                            <strong>Hallo {{nameUser}},</strong>
                        </p>

                        <!-- Main Message -->
                        <p style="color: #4a5568; font-size: 16px; line-height: 1.6; margin: 0 0 25px 0;">
                            {{description}}
                        </p>

                        <!-- Security Information -->
                        <div class="security-highlight">
                            <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <td>
                                        <div style="display: flex; align-items: center; margin-bottom: 12px;">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" style="margin-right: 12px;">
                                                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L14.17 3.17L9.83 7.51C9.23 8.11 9.23 9.05 9.83 9.65L10.93 10.75L5.51 16.17C5.12 16.56 5.12 17.18 5.51 17.59L6.41 18.49C6.8 18.88 7.42 18.88 7.83 18.49L13.25 13.07L14.35 14.17C14.95 14.77 15.89 14.77 16.49 14.17L20.83 9.83L21.5 10.5L23 9V7H21Z" fill="#f59e0b"/>
                                            </svg>
                                            <h3 style="color: #92400e; font-size: 16px; font-weight: 600; margin: 0;">
                                                Wichtige Sicherheitshinweise
                                            </h3>
                                        </div>
                                        <p style="color: #92400e; font-size: 14px; margin: 0; line-height: 1.5;">
                                            Diese E-Mail wurde aufgrund einer Passwort-Zurücksetzungsanfrage für Ihr Bridge-Konto gesendet.
                                            Falls Sie diese Anfrage nicht gestellt haben, ignorieren Sie diese E-Mail bitte.
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <!-- Call to Action -->
                        <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="margin: 35px 0 25px 0;">
                            <tr>
                                <td align="center">
                                    <a href="{{resetLink}}" target="_blank" class="cta-button" style="background: linear-gradient(135deg, #e32652 0%, #c41e3a 100%); color: white !important; padding: 16px 32px; border-radius: 8px; font-weight: 600; font-size: 16px; text-decoration: none; display: inline-block; box-shadow: 0 4px 12px rgba(227, 38, 82, 0.3);">
                                        🔑 Passwort jetzt festlegen
                                    </a>
                                </td>
                            </tr>
                        </table>

                        <!-- Security Tips -->
                        <div class="security-tips">
                            <h4 style="color: #0c4a6e; font-size: 16px; font-weight: 600; margin: 0 0 15px 0;">
                                🛡️ Tipps für ein sicheres Passwort:
                            </h4>
                            <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <td style="padding: 4px 0;">
                                        <div style="display: flex; align-items: center;">
                                            <span style="color: #10b981; font-size: 16px; margin-right: 12px;">✓</span>
                                            <span style="color: #1e40af; font-size: 14px;">Mindestens 8 Zeichen verwenden</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="padding: 4px 0;">
                                        <div style="display: flex; align-items: center;">
                                            <span style="color: #10b981; font-size: 16px; margin-right: 12px;">✓</span>
                                            <span style="color: #1e40af; font-size: 14px;">Groß- und Kleinbuchstaben kombinieren</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="padding: 4px 0;">
                                        <div style="display: flex; align-items: center;">
                                            <span style="color: #10b981; font-size: 16px; margin-right: 12px;">✓</span>
                                            <span style="color: #1e40af; font-size: 14px;">Zahlen und Sonderzeichen einbauen</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="padding: 4px 0;">
                                        <div style="display: flex; align-items: center;">
                                            <span style="color: #10b981; font-size: 16px; margin-right: 12px;">✓</span>
                                            <span style="color: #1e40af; font-size: 14px;">Keine persönlichen Daten verwenden</span>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <!-- Additional Information -->
                        <div class="info-box">
                            <p style="color: #4a5568; font-size: 14px; margin: 0; line-height: 1.5;">
                                <strong>🕐 Wichtiger Hinweis:</strong> Dieser Link ist aus Sicherheitsgründen nur für eine begrenzte Zeit gültig.
                                Falls der Link abgelaufen ist, können Sie eine neue Passwort-Zurücksetzung anfordern.
                            </p>
                        </div>

                        <!-- Footer Message -->
                        <p style="color: #4a5568; font-size: 16px; line-height: 1.6; margin: 30px 0 0 0; text-align: center;">
                            <strong>Viele Grüße,<br>Dein Bridge Team</strong>
                        </p>

                    </td>
                </tr>
            </table>

            <!-- Footer -->
            <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="margin-top: 40px;">
                <tr>
                    <td align="center">
                        <img src="https://firebasestorage.googleapis.com/v0/b/bridge-public-v2/o/dist%2Fimages%2Fbridge-logo-fae1080e.png?alt=media&token=a0a04c7d-33b4-44bb-b97a-fd1a96a493e3"
                             alt="Bridge Logo"
                             width="120"
                             style="display: block; margin-bottom: 20px; opacity: 0.8;">
                    </td>
                </tr>
                <tr>
                    <td align="center">
                        <p style="color: #718096; font-size: 14px; line-height: 1.5; margin: 0;">
                            <strong>Berufe4you UG</strong><br>
                            Logistikpark 7<br>
                            95448 Bayreuth<br><br>
                            <a href="mailto:<EMAIL>" style="color: #e32652; text-decoration: none;"><EMAIL></a>
                        </p>
                    </td>
                </tr>
            </table>

        </td>
    </tr>
</table>
</body>
</html>