import { Injectable, NotFoundException } from '@nestjs/common'
import { CreateMessageInput } from './dto/create-message.input'
import { UpdateMessageInput } from './dto/update-message.input'
import { PrismaService } from '../prisma.service'
import { Message } from './entities/message.entity'
import {
    CreateEmailNotificationInput,
    CreateNotificationInput,
} from '../notification/dto/create-notification.input'
import { SendNotificationParams } from '../notification/type/notificationTypes'
import { NotificationService } from '../notification/notification.service'
import { JobActionsService } from '../job-actions/job-actions.service'
import { PusherPubSubService } from '../pub-sub/pusher-pub-sub.service'
import { AppointmentService } from '../appointment/appointment.service'

@Injectable()
export class MessageService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly notificationService: NotificationService,
        private readonly jobActionService: JobActionsService,
        private readonly pusherService: PusherPubSubService,
        private readonly appointmentService: AppointmentService
    ) {}

    private async createMessage(
        chatRoomId: string,
        createMessageInput: CreateMessageInput,
        companyUserId?: string,
        applicantId?: string,
        companyFairContactPersonId?: string
    ) {
        if (!createMessageInput.content)
            throw new Error('Message content is required')
        return this.prisma.message.create({
            data: {
                ...createMessageInput,
                isApplicant: Boolean(applicantId),
                isCompany: !Boolean(applicantId),
                isDelivered: true,
                chatRoom: { connect: { id: chatRoomId } },
                ...(companyUserId && {
                    companyUser: { connect: { id: companyUserId } },
                }),
                ...(applicantId && {
                    applicant: { connect: { id: applicantId } },
                }),
                ...(companyFairContactPersonId && {
                    companyFairContactPerson: {
                        connect: { id: companyFairContactPersonId },
                    },
                }),
            },
            include: {
                chatRoom: true,
                companyUser: true,
                applicant: true,
                companyFairContactPerson: true,
            },
        })
    }

    private async getChatRoom(chatRoomId: string) {
        return this.prisma.chatRoom.findUnique({
            where: { id: chatRoomId },
            include: {
                appointment: {
                    include: {
                        applicant: true,
                    },
                },
                jobAction: {
                    include: {
                        applicant: true,
                    },
                },
            },
        })
    }

    private async updateJobAction(
        jobActionId: string,
        companyUserId?: string,
        applicantId?: string
    ) {
        let updatedJobAction = null
        const updateData = companyUserId
            ? { applicantIsNew: true }
            : applicantId
              ? { companyIsNew: true }
              : null

        if (updateData) {
            updatedJobAction = await this.jobActionService.update(
                {
                    id: jobActionId,
                    ...updateData,
                },
                companyUserId
            )
        }

        return updatedJobAction
    }
    async create(
        chatRoomId: string,
        createMessageInput: CreateMessageInput,
        companyUserId?: string,
        applicantId?: string,
        companyFairContactPersonId?: string
    ) {
        try {
            const [newMessage, chatRoom] = await Promise.all([
                this.createMessage(
                    chatRoomId,
                    {
                        ...createMessageInput,
                        isApplicant: Boolean(applicantId),
                    },
                    companyUserId,
                    applicantId,
                    companyFairContactPersonId
                ),
                this.getChatRoom(chatRoomId),
            ])

            if (chatRoom?.jobAction?.id) {
                await this.updateJobAction(
                    chatRoom.jobAction.id,
                    companyUserId,
                    applicantId
                )
            }

            if (chatRoom?.appointment?.id) {
                await this.appointmentService.setAppointmentAsNew(
                    chatRoom.appointment.id
                )
            }

            const bApplicantId =
                applicantId ||
                chatRoom?.jobAction?.applicantId ||
                chatRoom?.appointment?.applicant?.id

            await this.publishToLiveChatMessages(chatRoomId, newMessage)

            await this.notifyNewChatMessage(
                {
                    isNew: true,
                    type: createMessageInput.isCompany
                        ? 'newCompanyChat'
                        : 'newApplicantChat',
                },
                {
                    emailSubject: `Neue Nachricht `,
                    emailBody: `Sie haben eine neue Chat-Nachricht: ${createMessageInput.content}`,
                },
                chatRoom.jobAction?.jobAdvertId,
                bApplicantId,
                companyUserId,
                chatRoomId,
                chatRoom?.appointment?.id,
                companyFairContactPersonId,
                createMessageInput.isCompany
                    ? 'newCompanyChat'
                    : 'newApplicantChat',
                newMessage.content
            )

            return newMessage
        } catch (error) {
            console.error('Error creating message:', error)
            throw new Error('Failed to create message')
        }
    }

    async findAll() {
        return this.prisma.message.findMany({
            include: {
                chatRoom: true,
                companyUser: true,
                applicant: true,
            },
        })
    }

    async findOne(id: string): Promise<Message> {
        let message = null
        try {
            message = this.prisma.message.findUnique({
                where: {
                    id: id,
                },
                include: {
                    chatRoom: true,
                    companyUser: true,
                    applicant: true,
                },
            })
            if (!message) new NotFoundException('Message not found')
        } catch (error) {
            throw new NotFoundException(error.message)
        }

        return message
    }

    async update(
        id: string,
        chatRoomId: string,
        companyUserId: string,
        applicantId: string,
        updateMessageInput: UpdateMessageInput
    ): Promise<Message> {
        return this.prisma.message.update({
            where: {
                id: id,
            },
            data: {
                chatRoom: {
                    connect: {
                        id: chatRoomId,
                    },
                },
                companyUser: {
                    connect: {
                        id: companyUserId,
                    },
                },
                applicant: {
                    connect: {
                        id: applicantId,
                    },
                },
                ...updateMessageInput,
            },
        })
    }

    // Bulk update method to mark all messages from an applicant as seen
    async markMessagesAsSeen(
        chatRoomId: string,
        applicantId: string
    ): Promise<{ count: number }> {
        const result = await this.prisma.message.updateMany({
            where: {
                chatRoomId: chatRoomId,
                applicantId: applicantId,
                isApplicant: true,
                isSeen: false,
            },
            data: {
                isSeen: true,
            },
        })

        return { count: result.count }
    }

    async publishToLiveChatMessages(chatRoomId: string, newMessage: any) {
        const chatMsg = {
            id: newMessage?.id,
            content: newMessage?.content,
            createdAt: newMessage?.createdAt,
            authorName: newMessage?.authorName,
            isCompany: newMessage?.isCompany,
            isApplicant: newMessage?.isApplicant,
            applicantId: newMessage?.applicantId,
            companyUserId: newMessage?.companyUserId,
        }

        await this.pusherService.publish(
            `private-liveChatMessages.${chatRoomId}`,
            'created',
            chatMsg
        )
    }

    // async notifyNewChatMessage(
    //     createNotificationInput: CreateNotificationInput,
    //     createEmailNotificationInput: CreateEmailNotificationInput,
    //     jobAdvertId?: string,
    //     applicantId?: string,
    //     companyUserId?: string,
    //     chatRoomId?: string,
    //     companyFairContactPersonId?: string,
    //     type = 'newCompanyChat'
    // ) {
    //     let jobAdvert = null
    //     if (jobAdvertId) {
    //         jobAdvert = await this.prisma.jobAdvert.findUnique({
    //             where: {
    //                 id: jobAdvertId,
    //             },
    //             include: {
    //                 company: true,
    //             },
    //         })
    //         if (!jobAdvert) throw new NotFoundException('JobAdvert not found')
    //     }
    //
    //     const bCompanyUserId = companyUserId || jobAdvert?.companyUserId
    //
    //     const params: SendNotificationParams = {
    //         jobAdTitle: jobAdvert?.title,
    //         jobAdId: jobAdvert?.id,
    //
    //         chatId: chatRoomId,
    //         type: type,
    //         createNotificationInput,
    //     }
    //
    //     if (type === 'newCompanyChat') {
    //         console.log('we sending to applicant from company')
    //         params.createPushNotificationInput = {
    //             title: `Neue Nachricht von ${jobAdvert?.company.name}`,
    //             body: `Schaue in das Match zu ${jobAdvert?.title}`,
    //             actionUrl: `/job-advert/${jobAdvert?.id}`,
    //         }
    //         params.applicantId = applicantId
    //         await this.notificationService.sendPushNotification(params)
    //     } else {
    //         console.log('we sending to company for applicant')
    //         params.createEmailNotificationInput = createEmailNotificationInput
    //         params.companyUserId = bCompanyUserId
    //         await this.notificationService.sendEmailNotification(params)
    //     }
    // }
    // Data retrieval functions
    async getJobAdvertDetails(jobAdvertId: string) {
        const jobAdvert = await this.prisma.jobAdvert.findUnique({
            where: { id: jobAdvertId },
            include: { company: true },
        })
        if (!jobAdvert) throw new NotFoundException('JobAdvert not found')
        return jobAdvert
    }

    async getCompanyFairContactPersonDetails(
        companyFairContactPersonId: string
    ) {
        const companyFairContactPerson =
            await this.prisma.companyFairContactPerson.findUnique({
                where: { id: companyFairContactPersonId },
                include: {
                    contactPerson: true,
                    companyFairParticipation: {
                        include: {
                            company: true,
                        },
                    },
                },
            })
        if (!companyFairContactPerson)
            throw new NotFoundException('CompanyFairContactPerson not found')
        return companyFairContactPerson
    }

    buildJobAdvertNotificationParams(
        jobAdvert: any,
        chatRoomId: string,
        type: string,
        createNotificationInput: CreateNotificationInput,
        applicantId: string
    ): SendNotificationParams {
        return {
            jobAdTitle: jobAdvert?.title,
            jobAdId: jobAdvert?.id,
            chatId: chatRoomId,
            type: type,
            createNotificationInput,
            createPushNotificationInput: {
                title: `Neue Nachricht von ${jobAdvert?.company.name}`,
                body: `Schaue in das Match zu ${jobAdvert?.title}`,
                actionUrl: `/job-advert/${jobAdvert?.id}`,
            },
            applicantId: applicantId,
        }
    }

    buildAppointmentChatNotificationParams(
        company: any,
        chatRoomId: string,
        createNotificationInput: CreateNotificationInput,
        msgContent: string,
        applicantId: string,
        appointmentId?: string
    ): SendNotificationParams {
        return {
            chatId: chatRoomId,
            type: 'newAppointmentChat',
            createNotificationInput,
            createPushNotificationInput: {
                title: `Neue Nachricht von ${company?.name}`,
                body:
                    msgContent.length > 30
                        ? msgContent.substring(0, 30) + '...'
                        : msgContent,
                actionUrl: `/fair`,
            },
            applicantId: applicantId,
            appointmentId: appointmentId,
        }
    }

    async notifyNewChatMessage(
        createNotificationInput: CreateNotificationInput,
        createEmailNotificationInput: CreateEmailNotificationInput,
        jobAdvertId?: string,
        applicantId?: string,
        companyUserId?: string,
        chatRoomId?: string,
        appointmentId?: string,
        companyFairContactPersonId?: string,
        type = 'newCompanyChat',
        msgContent?: string
    ) {
        if (appointmentId) {
            const companyFairContactPerson =
                await this.getCompanyFairContactPersonDetails(
                    companyFairContactPersonId
                )

            const company =
                companyFairContactPerson?.companyFairParticipation?.company

            const params = this.buildAppointmentChatNotificationParams(
                company,
                chatRoomId,
                createNotificationInput,
                msgContent,
                applicantId,
                appointmentId
            )

            if (type === 'newCompanyChat') {
                await this.notificationService.sendPushNotification(params)
            } else {
                params.createEmailNotificationInput =
                    createEmailNotificationInput
                params.companyFairContactPersonId = companyFairContactPersonId
                await this.notificationService.sendContactPersonEmailNotification(
                    params
                )
            }
        } else if (jobAdvertId) {
            const jobAdvert = await this.getJobAdvertDetails(jobAdvertId)
            const params = this.buildJobAdvertNotificationParams(
                jobAdvert,
                chatRoomId,
                type,
                createNotificationInput,
                applicantId
            )

            if (type === 'newCompanyChat') {
                await this.notificationService.sendPushNotification(params)
            } else {
                params.createEmailNotificationInput =
                    createEmailNotificationInput
                params.companyUserId = companyUserId || jobAdvert?.companyUserId
                await this.notificationService.sendEmailNotification(params)
            }
        } else {
            throw new Error(
                'Either jobAdvertId or companyFairContactPersonId must be provided.'
            )
        }
    }
}
