export const generateRandomString = (length: number = 6): string => {
    const characters =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    return Array.from(
        { length },
        () => characters[Math.floor(Math.random() * characters.length)]
    ).join('')
}

/**
 * Formats a Date object to a German-style date and time string
 * @param date - The Date object to format
 * @returns A formatted string in the format "DD.MM.YYYY, HH:MM"
 * @example
 * formatDateTime(new Date('2024-03-15T14:30:00')) // returns "15.03.2024, 14:30"
 */
export const formatDateTime = (date: Date): string => {
    console.log('reservationdate::', date)
    const day = date.getDate().toString().padStart(2, '0')
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const year = date.getFullYear().toString()
    return `${day}.${month}.${year}, ${hours}:${minutes}`
}
