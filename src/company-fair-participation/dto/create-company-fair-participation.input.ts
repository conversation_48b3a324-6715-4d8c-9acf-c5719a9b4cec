import { InputType, Field } from '@nestjs/graphql'
import { IsString, IsOptional } from 'class-validator'

@InputType()
export class CreateCompanyFairParticipationInput {
    @IsString()
    @Field()
    companyId: string

    @IsString()
    @Field()
    fairId: string

    @IsOptional()
    @IsString({ each: true })
    @Field(() => [String], { nullable: true })
    categoryIds: string[]

    @IsOptional()
    @IsString({ each: true })
    @Field(() => [String], { nullable: true })
    partnerLinkIds?: string[]
}
