import { Injectable, Logger } from '@nestjs/common'
import { MailerService } from '@nestjs-modules/mailer'
import {
    NewCompanyUserInput,
    ResetPasswordInput,
    WelcomeEmailInput,
} from './dto/welcome-email.input'
import { NewLikeInput } from './dto/newLike.input'
import { NewJobAdInput } from './dto/newJobAd.input'
import { NewChatInput } from './dto/newChat.input'
import { NewAppointmentInput } from './dto/new-appointment.input'
import { ConfigService } from '@nestjs/config'
import { formatDateTime } from '../utils'

@Injectable()
export class EmailServerService {
    constructor(
        private readonly mailerService: MailerService,
        private configService: ConfigService
    ) {}

    async sendResetPasswordEmail(data: ResetPasswordInput) {
        const { email, resetLink } = data
        let response = ''
        try {
            await this.mailerService.sendMail({
                to: email,
                from: '<EMAIL>',
                subject: 'Bitte setzen Sie Ihr Passwort zurück',
                text: 'Passwort zurücksetzen!',
                template: 'resetPassword',
                context: {
                    title: 'Passwort zurücksetzen',
                    description:
                        'Sie müssen Ihr Passwort zurücksetzen? Kein Problem! Klicken Sie einfach auf die Schaltfläche unten.✔ Wenn Sie keine Passwortzurücksetzung angefordert haben, ignorieren Sie diese E-Mail bitte.✔',
                    nameUser: email,
                    name: email,
                    resetLink: resetLink,
                },
            })
            Logger.log('[MailService] Password Reset: Email send successfully!')
            response = 'Email sent successfully'
        } catch (error) {
            Logger.error(
                '[MailService] Password Reset: Send Mail failed!',
                error
            )
            response = 'Email send failed'
        }
        return response
    }

    async sendNewUserEmail(data: NewCompanyUserInput) {
        const { email, resetLink, name } = data
        let response = ''
        try {
            await this.mailerService.sendMail({
                to: email,
                from: '<EMAIL>',
                subject: 'Willkommen bei Bridge!',
                text: 'Hallo!',
                template: 'resetPassword',
                context: {
                    title: 'Willkommen bei Bridge! ',
                    description:
                        'willkommen bei Bridge! Um dein Passwort festzulegen und die Email Adresse zu bestätigen, klicke bitte auf nachfolgenden Link:',
                    nameUser: email,
                    name: name,
                    resetLink: resetLink,
                    footer: 'Viele Grüße,\n' + 'Dein Bridge Team',
                },
            })
            Logger.log(
                '[MailService] New Company User : Email send successfully!'
            )
            response = 'Email sent successfully'
        } catch (error) {
            Logger.error(
                '[MailService] New Company User: Send Mail failed!',
                error
            )
            response = 'Email send failed'
        }
        return response
    }

    async sendWelcomeEmail(data: WelcomeEmailInput) {
        const { email, verificationLink } = data
        let response = ''
        try {
            await this.mailerService.sendMail({
                to: email,
                from: '<EMAIL>',
                subject: 'Email bestätigen',
                text: 'Herzlich willkommen bei Bridge!',
                template: 'welcome',
                context: {
                    title: 'Welcome',
                    description:
                        'Um Ihre EMail Adresse zu verifizieren, klicken Sie bitte auf den Link. Direkt danach können Sie Ihre Stellenanzeigen im Dashboard erstellen.✔',
                    verificationLink: verificationLink,
                },
            })
            Logger.log(
                '[MailService] New Company: Welcome Email sent successfully!'
            )
            response = 'Email sent successfully'
        } catch (error) {
            Logger.error(
                '[MailService] Welcome Email: Send Mail failed!',
                error
            )
            response = 'Email send failed'
        }
        return response
    }

    async sendNewLikeEmail(data: NewLikeInput) {
        const { jobAdTitle, jobAdId, applicantName, email } = data
        let response = ''
        try {
            await this.mailerService.sendMail({
                to: email,
                from: '"Bridge App" <<EMAIL>>',
                subject: `Neue Bewerbung zu ${jobAdTitle}`,
                text: `Es gibt eine neue Bewerbung zu Ihrer Stellenanzeige: ${data['jobTitle']}`,
                template: 'newLike',
                context: {
                    jobAdTitle: jobAdTitle,
                    jobAdId: jobAdId,
                    applicantName: applicantName,
                    email: email,
                },
            })
            Logger.log('[MailService] New Like: Email send successfully!')
            response = 'Email sent successfully'
        } catch (error) {
            Logger.error('[MailService] New Like: Send Mail failed!', error)
            response = 'Email send failed'
        }
        return response
    }
    async sendNewChatEmail(data: NewChatInput) {
        const { applicantName, email, message, jobAdTitle, timeslot } = data
        console.log('timeslot::', timeslot)
        console.log('GrandData::', data)
        let response = ''
        try {
            if (jobAdTitle) {
                await this.mailerService.sendMail({
                    to: email,
                    from: '"Bridge App" <<EMAIL>>',
                    subject: `Neue Chat-Nachricht!`,
                    text: `Sie haben eine neue Chat-Nachricht`,
                    template: 'newChat',
                    context: {
                        jobAdTitle: jobAdTitle,
                        message: message,
                        applicantName: applicantName,
                        email: email,
                    },
                })
            } else {
                await this.mailerService.sendMail({
                    to: email,
                    from: '"Bridge App" <<EMAIL>>',
                    subject: `Neue Chat-Nachricht!`,
                    text: `Sie haben eine neue Chat-Nachricht`,
                    template: 'newAppointmentChat',
                    context: {
                        jobAdTitle: jobAdTitle,
                        message: message,
                        applicantName: applicantName,
                        email: email,
                        reservationDate: formatDateTime(
                            timeslot?.reservationDate
                        ),
                    },
                })
            }

            Logger.log('[MailService] New Chat: Email send successfully!')
            response = 'Email sent successfully'
        } catch (error) {
            Logger.error('[MailService] New Chat: Send Mail failed!', error)
            response = 'Email send failed'
        }
        return response
    }
    async sendNewJobAdEmail(data: NewJobAdInput) {
        const { jobAdTitle, companyId, jobAdId } = data
        let response = ''
        try {
            await this.mailerService.sendMail({
                to: '<EMAIL>',
                from: '"Bridge App" <<EMAIL>>',
                subject: `Neue Bewerbung zu ${jobAdTitle}`,
                text: `Sie haben eine neue Stellenanzeige erhalten, die genehmigt werden muss ${data['jobTitle']}`,
                template: 'newJobAd',
                context: {
                    jobAdTitle: jobAdTitle,
                    jobAdvertId: jobAdId,
                    companyId: companyId,
                },
            })
            Logger.log('[MailService] New JobAd: Email send successfully!')
            response = 'Email sent successfully'
        } catch (error) {
            Logger.error('[MailService] New JobAd: Send Mail failed!', error)
            response = 'Email send failed'
        }
        return response
    }

    async sendNewAppointmentEmail(
        data: NewAppointmentInput,
        adminEmail: string
    ) {
        const {
            applicantName,
            companyName,
            fairName,
            contactPersonName,
            appointmentDate,
            appointmentStatus,
            appointmentId,
            applicantId,
        } = data
        let response = ''
        const appointmentLink = `${this.configService.get(
            'GLOBAL.BRIDGE_FRONT_HOST'
        )}/fair/appointments/${appointmentId}/applicant/${applicantId}`
        try {
            await this.mailerService.sendMail({
                to: adminEmail,
                from: '"Bridge App" <<EMAIL>>',
                subject: `Neue Gesprächsanfrage erhalten`,
                text: `Sie haben eine neue Gesprächsanfrage erhalten! Bitte bestätigen Sie diese oder lehnen Sie diese zeitnah ab. Sie finden eine Übersicht aller Gesprächsanfragen in Ihrem Dashboard.`,
                template: 'newAppointment',
                context: {
                    applicantName,
                    companyName,
                    fairName,
                    contactPersonName,
                    appointmentDate,
                    appointmentStatus,
                    appointmentLink,
                },
            })
            Logger.log(
                '[MailService] New Appointment: Email sent successfully to FGAdmin!'
            )
            response = 'Email sent successfully'
        } catch (error) {
            Logger.error(
                '[MailService] New Appointment: Send Mail failed!',
                error
            )
            response = 'Email send failed'
        }
        return response
    }

    async sendCanceledAppointmentEmail(
        data: NewAppointmentInput,
        adminEmail: string
    ) {
        const {
            applicantName,
            companyName,
            fairName,
            contactPersonName,
            appointmentDate,
            appointmentStatus,
            appointmentId,
            applicantId,
        } = data
        let response = ''
        const appointmentLink = `${this.configService.get(
            'GLOBAL.BRIDGE_FRONT_HOST'
        )}/fair/appointments/${appointmentId}/applicant/${applicantId}`
        try {
            await this.mailerService.sendMail({
                to: adminEmail,
                from: '"Bridge App" <<EMAIL>>',
                subject: `Termin wurde abgesagt`,
                text: `Der Bewerber hat Ihren bestätigten Termin abgesagt`,
                template: 'canceledAppointment',
                context: {
                    applicantName,
                    companyName,
                    fairName,
                    contactPersonName,
                    appointmentDate,
                    appointmentStatus,
                    appointmentLink,
                },
            })
            Logger.log(
                '[MailService] Cancelled Appointment: Email sent successfully to FGAdmin!'
            )
            response = 'Email sent successfully'
        } catch (error) {
            Logger.error(
                '[MailService] Cancelled Appointment: Send Mail failed!',
                error
            )
            response = 'Email send failed'
        }
        return response
    }
}
