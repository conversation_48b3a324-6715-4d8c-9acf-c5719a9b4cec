import { Module } from '@nestjs/common'
import { AppointmentService } from './appointment.service'
import { AppointmentResolver } from './appointment.resolver'
import { AppointmentPublisherService } from './appointment-publisher/appointment-publisher.service'
import { PusherPubSubModule } from '../pub-sub/pusher-pub-sub.module'
import { PubSubModule } from '../pub-sub/pub-sub.module'
import { AuthModule } from '../auth/auth.module'
import { ChatRoomModule } from '../chat-room/chat-room.module'
import { NotificationModule } from '../notification/notification.module'
import { EmailServerModule } from '../email-server/email-server.module'

@Module({
    exports: [AppointmentService],
    imports: [
        PusherPubSubModule,
        AuthModule,
        ChatRoomModule,
        NotificationModule,
        EmailServerModule,
    ],
    providers: [
        AppointmentResolver,
        AppointmentService,
        AppointmentPublisherService,
    ],
})
export class AppointmentModule {}
