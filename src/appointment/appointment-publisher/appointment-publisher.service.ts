import { Injectable } from '@nestjs/common'
import { PusherPubSubService } from '../../pub-sub/pusher-pub-sub.service'

@Injectable()
export class AppointmentPublisherService {
    constructor(private readonly pusherService: PusherPubSubService) {}

    private readonly eventNames = {
        appointmentCreated: 'appointmentCreated',
        appointmentUpdated: 'appointmentUpdated',
        appointmentDeleted: 'appointmentDeleted',
    }

    private formatSlimAppointment(appointment: any) {
        return {
            id: appointment?.id,
            status: appointment?.status,
            applicantId: appointment?.applicant?.id,
            name:
                appointment?.applicant?.firstName +
                ' ' +
                appointment?.applicant?.lastName,
            birthday: appointment?.applicant?.birthDate,
            image: appointment?.applicant?.profileImageUrl,
            logoImageUrl:
                appointment?.contactPersonTimeslot?.companyFairContactPerson
                    ?.companyFairParticipation?.company?.logoImageUrl,
            reservationDate: appointment?.reservationDate,
            companyIsNew: appointment?.companyIsNew,
            fairName:
                appointment.contactPersonTimeslot.companyFairContactPerson
                    .companyFairParticipation.fair.name,
            fairAddress:
                appointment.contactPersonTimeslot.companyFairContactPerson
                    .companyFairParticipation.fair.location,
            fairCity:
                appointment.contactPersonTimeslot.companyFairContactPerson
                    .companyFairParticipation.fair.city,
        }
    }
    private formatSlimAppointmentForApplicant(appointment: any) {
        return {
            id: appointment?.id,
            status: appointment?.status,
            start: appointment?.contactPersonTimeslot?.startTime,
            end: appointment?.contactPersonTimeslot?.endTime,
            applicantIsNew: appointment?.applicantIsNew,
            reservationDate: appointment?.reservationDate,
            chatRoomId: appointment?.chatRoomId || appointment?.chatRoom?.id,
            contactPersonName:
                appointment?.contactPersonTimeslot?.companyFairContactPerson
                    ?.contactPerson?.name,
            logoImageUrl:
                appointment?.contactPersonTimeslot?.companyFairContactPerson
                    ?.companyFairParticipation?.company?.logoImageUrl,
            companyName:
                appointment?.contactPersonTimeslot.companyFairContactPerson
                    ?.companyFairParticipation?.company?.name,
            companyIsNew: appointment?.companyIsNew,
            fairName:
                appointment.contactPersonTimeslot.companyFairContactPerson
                    .companyFairParticipation.fair.name,
            fairAddress:
                appointment.contactPersonTimeslot.companyFairContactPerson
                    .companyFairParticipation.fair.location,
            fairCity:
                appointment.contactPersonTimeslot.companyFairContactPerson
                    .companyFairParticipation.fair.city,
            rejectReason: appointment?.rejectReason,
        }
    }

    async publishOnUpdate(
        appointment: any,
        applicantId: string,
        contactPersonId: string
    ) {
        if (appointment.status === 'REQUESTED') {
            await this.publishToLiveContactPersons(
                contactPersonId,
                appointment,
                this.eventNames.appointmentUpdated
            )
            await this.publishToLiveApplicants(
                applicantId,
                appointment,
                this.eventNames.appointmentUpdated
            )
        } else {
            await this.publishToLiveApplicants(
                applicantId,
                appointment,
                this.eventNames.appointmentUpdated
            )
        }
    }

    async publishOnCreate(
        appointment: any,
        contactPersonId: string
    ): Promise<void> {
        await this.publishToLiveContactPersons(
            contactPersonId,
            appointment,
            this.eventNames.appointmentCreated
        )

        await this.publishToLiveApplicants(
            appointment.applicantId,
            appointment,
            this.eventNames.appointmentCreated
        )
    }

    async publishOnDelete(
        applicantId: string,
        appointment: any,
        contactPersonId: string
    ) {
        await this.publishToLiveApplicants(
            applicantId,
            appointment,
            this.eventNames.appointmentDeleted
        )

        await this.publishToLiveContactPersons(
            contactPersonId,
            appointment,
            this.eventNames.appointmentDeleted
        )
    }

    async publishToLiveContactPersons(
        contactPersonId: string,
        appointment: any,
        eventName: string
    ) {
        const appointmentData = this.formatSlimAppointment(appointment)

        const channel = `liveContactPersonAppointments`

        try {
            await this.pusherService.publish(
                `private-${channel}`,
                eventName,
                appointmentData
            )
        } catch (err) {
            console.log('Contact person appointment publish error', err)
        }
    }

    async publishToLiveApplicants(
        applicantId: string,
        appointment: any,
        eventName: string
    ) {
        const slimAppointment =
            this.formatSlimAppointmentForApplicant(appointment)

        try {
            const channel = `liveApplicantAppointments.${applicantId}`

            await this.pusherService.publish(
                `private-${channel}`,
                eventName,
                slimAppointment
            )
        } catch (err) {
            console.log('Applicant appointment publish error', err)
        }
    }
}
