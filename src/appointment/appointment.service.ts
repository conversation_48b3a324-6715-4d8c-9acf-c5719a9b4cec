import {
    Injectable,
    NotFoundException,
    BadRequestException,
} from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateAppointmentInput } from './dto/create-appointment.input'
import { UpdateAppointmentInput } from './dto/update-appointment.input'
import { ActionState, AppointmentStatus, FairStatus } from '@prisma/client'
import { AppointmentPublisherService } from './appointment-publisher/appointment-publisher.service'
import {
    AppointmentFilterInput,
    TimeRangeInput,
} from './dto/appointment-filter.input'
import { ChatRoomService } from '../chat-room/chat-room.service'
import { CreateNotificationInput } from '../notification/dto/create-notification.input'
import { NotificationService } from '../notification/notification.service'
import { EmailServerService } from '../email-server/email-server.service'

@Injectable()
export class AppointmentService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly appointmentPublisherService: AppointmentPublisherService,
        private readonly chatRoomService: ChatRoomService,
        private readonly notificationService: NotificationService,
        private readonly emailService: EmailServerService
    ) {}

    private appointmentIncludes() {
        return {
            chatRoom: {
                include: {
                    messages: true,
                },
            },
            applicant: true,
            contactPersonTimeslot: {
                include: {
                    companyFairContactPerson: {
                        include: {
                            contactPerson: true,
                            companyFairParticipation: {
                                include: {
                                    company: true,
                                    fair: true,
                                },
                            },
                        },
                    },
                },
            },
        }
    }

    async create(createAppointmentInput: CreateAppointmentInput, user?: any) {
        const { applicantId, contactPersonTimeslotId } = createAppointmentInput
        const applicant = await this.prisma.applicant.findUnique({
            where: { id: applicantId },
        })

        if (!applicant) {
            throw new NotFoundException('Applicant not found')
        }

        const timeslot = await this.prisma.contactPersonTimeslot.findUnique({
            where: { id: contactPersonTimeslotId },
            include: {
                Appointment: true,
            },
        })
        const reservationDate = this.dateWithZeroTime(timeslot.startTime)

        if (!timeslot) {
            throw new NotFoundException('Contact person timeslot not found')
        }

        if (!timeslot.available || timeslot.Appointment) {
            throw new BadRequestException(
                'Timeslot is not available for booking'
            )
        }

        const newAppointment = await this.prisma.appointment.create({
            data: {
                applicant: {
                    connect: { id: applicantId },
                },
                contactPersonTimeslot: {
                    connect: { id: contactPersonTimeslotId },
                },
                status: AppointmentStatus.REQUESTED,
                reservationDate: reservationDate,
                applicantIsNew: false,
                companyIsNew: true,
            },
            include: this.appointmentIncludes(),
        })

        await this.prisma.contactPersonTimeslot.update({
            where: { id: contactPersonTimeslotId },
            data: { available: false },
        })

        const chatRoom = await this.chatRoomService.createAppointmentChatRoom(
            newAppointment.id,
            { status: 'active' }
        )

        const contactPersonId =
            newAppointment.contactPersonTimeslot.companyFairContactPerson
                .contactPersonId

        await this.appointmentPublisherService.publishOnCreate(
            { ...newAppointment, chatRoomId: chatRoom?.id },
            contactPersonId
        )

        // Send email notification to FGAdmin about the new appointment
        await this.sendFGAdminAppointmentNotification(newAppointment)

        return newAppointment
    }

    async sendFGAdminAppointmentNotification(appointment: any) {
        try {
            // Get all FGAdmins
            const fgAdmins = await this.prisma.fGAdmin.findMany()

            if (fgAdmins.length === 0) {
                console.log('No FGAdmins found to notify')
                return
            }

            // Extract appointment details for the email
            const applicantName = `${appointment.applicant.firstName || ''} ${
                appointment.applicant.lastName || ''
            }`.trim()
            const applicantId = appointment.applicant.id
            const companyName =
                appointment.contactPersonTimeslot.companyFairContactPerson
                    .companyFairParticipation.company.name
            const fairName =
                appointment.contactPersonTimeslot.companyFairContactPerson
                    .companyFairParticipation.fair.name
            const contactPersonName =
                appointment.contactPersonTimeslot.companyFairContactPerson
                    .contactPerson.name
            const appointmentDate = new Date(
                appointment.contactPersonTimeslot.startTime
            ).toLocaleString('de-DE', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
            })

            // Prepare appointment data for email
            const appointmentData = {
                applicantName,
                companyName,
                fairName,
                contactPersonName,
                appointmentDate,
                appointmentStatus: appointment.status,
                appointmentId: appointment.id,
                applicantId,
            }

            // Send email to each FGAdmin
            for (const admin of fgAdmins) {
                await this.emailService.sendNewAppointmentEmail(
                    appointmentData,
                    admin.email
                )
            }
        } catch (error) {
            console.error(
                '[MailService] New Appointment: Send Mail failed!',
                error
            )
        }
    }

    async findAll() {
        return this.prisma.appointment.findMany({
            include: this.appointmentIncludes(),
        })
    }

    async findOne(id: string) {
        const appointment = await this.prisma.appointment.findUnique({
            where: { id },
            include: this.appointmentIncludes(),
        })

        if (!appointment) {
            throw new NotFoundException('Appointment not found')
        }

        return appointment
    }

    async update(id: string, updateAppointmentInput: UpdateAppointmentInput) {
        const { status, rejectReason } = updateAppointmentInput

        let appointmentStatus = null as AppointmentStatus

        const appointment = await this.findOne(id)

        if (
            appointment.status === AppointmentStatus.CONFIRMED &&
            status === AppointmentStatus.CONFIRMED
        ) {
            throw new BadRequestException('Appointment already confirmed')
        }

        if (
            appointment.status === AppointmentStatus.CONFIRMED &&
            status === AppointmentStatus.REJECTED
        ) {
            appointmentStatus = AppointmentStatus.CANCELED
        } else {
            appointmentStatus = status
        }

        // Prepare data object for update
        const updateData: any = {
            status: appointmentStatus,
            applicantIsNew: true,
            companyIsNew: updateAppointmentInput.companyIsNew,
        }

        // Add rejectReason if provided and status is REJECTED or CANCELED
        if (
            rejectReason &&
            (appointmentStatus === AppointmentStatus.REJECTED ||
                appointmentStatus === AppointmentStatus.CANCELED)
        ) {
            updateData.rejectReason = rejectReason
        }

        const updatedAppointment = await this.prisma.appointment.update({
            where: { id },
            data: updateData,
            include: {
                applicant: true,
                chatRoom: true,
                contactPersonTimeslot: {
                    include: {
                        companyFairContactPerson: {
                            include: {
                                contactPerson: true,
                                companyFairParticipation: {
                                    include: {
                                        company: true,
                                        fair: true,
                                    },
                                },
                            },
                        },
                    },
                },
            },
        })
        await this.appointmentPublisherService.publishOnUpdate(
            updatedAppointment,
            updatedAppointment.applicantId,
            updatedAppointment.contactPersonTimeslot?.companyFairContactPerson
                ?.contactPersonId
        )

        const createNotificationInput: CreateNotificationInput = {
            isNew: true,
            type: 'newAppointmentChat',
        }

        if (updatedAppointment.status) {
            await this.notifyAppointmentStatusUpdate(
                createNotificationInput,
                updatedAppointment.applicantId,
                updatedAppointment.chatRoom?.id,
                updatedAppointment.id,
                updatedAppointment.contactPersonTimeslot
                    ?.companyFairContactPerson.companyFairParticipation.company
                    .name,
                status,
                updatedAppointment.contactPersonTimeslot
                    ?.companyFairContactPerson.contactPerson.name,
                updatedAppointment.contactPersonTimeslot.startTime
            )
        }

        return updatedAppointment
    }

    async remove(id: string, rejectReason?: string) {
        const appointment = await this.findOne(id)

        // Prepare data object for update
        const updateData: any = {
            status: AppointmentStatus.REJECTED,
        }

        // Add rejectReason if provided
        if (rejectReason) {
            updateData.rejectReason = rejectReason
        }

        const deletedAppointment = await this.prisma.appointment.update({
            data: updateData,
            where: {
                id: id,
            },
        })

        await this.appointmentPublisherService.publishOnDelete(
            deletedAppointment.applicantId,
            appointment,
            appointment.contactPersonTimeslot?.companyFairContactPerson
                ?.contactPersonId
        )

        return deletedAppointment
    }

    async findByApplicant(applicantId: string) {
        return this.prisma.appointment.findMany({
            where: { applicantId },
            include: {
                applicant: true,
                contactPersonTimeslot: {
                    include: {
                        companyFairContactPerson: {
                            include: {
                                contactPerson: true,
                                companyFairParticipation: {
                                    include: {
                                        company: true,
                                        fair: true,
                                    },
                                },
                            },
                        },
                    },
                },
            },
        })
    }

    async findByFair(fairId: string) {
        return this.prisma.appointment.findMany({
            where: {
                contactPersonTimeslot: {
                    companyFairContactPerson: {
                        companyFairParticipation: {
                            fairId,
                        },
                    },
                },
            },
            include: {
                applicant: true,
                contactPersonTimeslot: {
                    include: {
                        companyFairContactPerson: {
                            include: {
                                contactPerson: true,
                                companyFairParticipation: {
                                    include: {
                                        company: true,
                                        fair: true,
                                    },
                                },
                            },
                        },
                    },
                },
            },
        })
    }

    async findByCompany(companyId: string) {
        return this.prisma.appointment.findMany({
            where: {
                contactPersonTimeslot: {
                    companyFairContactPerson: {
                        companyFairParticipation: {
                            companyId,
                        },
                    },
                },
            },
            include: {
                applicant: true,
                contactPersonTimeslot: {
                    include: {
                        companyFairContactPerson: {
                            include: {
                                contactPerson: true,
                                companyFairParticipation: {
                                    include: {
                                        company: true,
                                        fair: true,
                                    },
                                },
                            },
                        },
                    },
                },
            },
        })
    }

    async findByContactPerson(contactPersonId: string) {
        return this.prisma.appointment.findMany({
            where: {
                contactPersonTimeslot: {
                    companyFairContactPersonId: contactPersonId,
                },
            },
            include: {
                applicant: true,
                contactPersonTimeslot: {
                    include: {
                        companyFairContactPerson: {
                            include: {
                                contactPerson: true,
                                companyFairParticipation: {
                                    include: {
                                        company: true,
                                        fair: true,
                                    },
                                },
                            },
                        },
                    },
                },
            },
        })
    }

    async findWithFilters(
        filter?: AppointmentFilterInput,
        skip?: number,
        take?: number
    ) {
        if (!filter) {
            const [appointments, totalCount] = await Promise.all([
                this.prisma.appointment.findMany({
                    include: this.appointmentIncludes(),
                    skip: skip ?? 0,
                    take: take ?? 10,
                }),
                this.prisma.appointment.count(),
            ])
            return { items: appointments || [], count: totalCount }
        }

        const {
            applicantId,
            contactPersonId,
            companyId,
            fairId,
            status,
            date,
            timeRange,
        } = filter

        const where: any = {}

        if (applicantId) {
            where.applicantId = applicantId
        }

        if (status) {
            where.status = status
        }

        if (contactPersonId) {
            where.contactPersonTimeslot = {
                companyFairContactPersonId: contactPersonId,
            }
        }

        if (companyId) {
            where.contactPersonTimeslot = {
                ...(where.contactPersonTimeslot || {}),
                companyFairContactPerson: {
                    ...(where.contactPersonTimeslot?.companyFairContactPerson ||
                        {}),
                    companyFairParticipation: {
                        companyId,
                    },
                },
            }
        }

        if (fairId) {
            where.contactPersonTimeslot = {
                ...(where.contactPersonTimeslot || {}),
                companyFairContactPerson: {
                    ...(where.contactPersonTimeslot?.companyFairContactPerson ||
                        {}),
                    companyFairParticipation: {
                        ...(where.contactPersonTimeslot
                            ?.companyFairContactPerson
                            ?.companyFairParticipation || {}),
                        fairId,
                    },
                },
            }
        }

        if (date) {
            // Convert the input date to start and end of the day for range comparison
            const startOfDay = this.getStartOfDay(date)
            const endOfDay = this.getEndOfDay(date)

            where.reservationDate = {
                gte: startOfDay,
                lte: endOfDay,
            }
        }

        const [appointments, totalCount] = await Promise.all([
            this.prisma.appointment.findMany({
                where,
                include: this.appointmentIncludes(),
                skip,
                take,
            }),
            this.prisma.appointment.count({ where }),
        ])

        if (timeRange?.startTime || timeRange?.endTime) {
            const filteredAppointments = this.filterByTimeRange(
                appointments,
                timeRange
            )
            return {
                items: filteredAppointments,
                count: filteredAppointments.length,
            }
        }

        return { items: appointments || [], count: totalCount }
    }

    // Convert a date to the start of the day (00:00:00.000)
    private getStartOfDay(inputDate: Date | string): Date {
        const date = inputDate instanceof Date ? inputDate : new Date(inputDate)
        if (isNaN(date.getTime())) {
            throw new Error('Invalid date provided')
        }

        const startOfDay = new Date(date)
        startOfDay.setHours(0, 0, 0, 0)
        return startOfDay
    }

    // Convert a date to the end of the day (23:59:59.999)
    private getEndOfDay(inputDate: Date | string): Date {
        const date = inputDate instanceof Date ? inputDate : new Date(inputDate)
        if (isNaN(date.getTime())) {
            throw new Error('Invalid date provided')
        }

        const endOfDay = new Date(date)
        endOfDay.setHours(23, 59, 59, 999)
        return endOfDay
    }

    private filterByTimeRange(appointments: any[], timeRange: TimeRangeInput) {
        // If no appointments or no time range filters, return the original array
        if (
            !appointments ||
            appointments.length === 0 ||
            (!timeRange.startTime && !timeRange.endTime)
        ) {
            return appointments || []
        }

        const startTime = timeRange.startTime
            ? new Date(timeRange.startTime)
            : null
        const endTime = timeRange.endTime ? new Date(timeRange.endTime) : null

        return appointments.filter((appointment) => {
            const timeslot = appointment.contactPersonTimeslot
            if (!timeslot || !timeslot.startTime) {
                return false
            }

            const timeslotStartTime = new Date(timeslot.startTime)

            const isAfterStart = startTime
                ? timeslotStartTime >= startTime
                : true
            const isBeforeEnd = endTime ? timeslotStartTime <= endTime : true

            return isAfterStart && isBeforeEnd
        })
    }
    private dateWithZeroTime = (dateString: Date | string): string | null => {
        const date =
            dateString instanceof Date ? dateString : new Date(dateString)
        if (isNaN(date.getTime())) {
            return null
        }
        date.setHours(0, 0, 0, 0)
        return date.toISOString()
    }

    getFilterOptions(appointments: any[]): {
        applicants: { id: string; text: string }[]
        contactPersons: { id: string; text: string }[]
        companies: { id: string; text: string }[]
        fairs: { id: string; text: string }[]
    } {
        const uniqueApplicants = new Map<string, { id: string; text: string }>()
        const uniqueContactPersons = new Map<
            string,
            { id: string; text: string }
        >()
        const uniqueCompanies = new Map<string, { id: string; text: string }>()
        const uniqueFairs = new Map<string, { id: string; text: string }>()

        appointments.forEach((appointment) => {
            if (appointment.applicant && appointment.applicant.id) {
                const applicant = appointment.applicant
                const text =
                    `${applicant.firstName || ''} ${
                        applicant.lastName || ''
                    }`.trim() || 'Unknown'
                uniqueApplicants.set(applicant.id, { id: applicant.id, text })
            }

            if (
                appointment.contactPersonTimeslot?.companyFairContactPerson
                    ?.contactPerson
            ) {
                const contactPerson =
                    appointment.contactPersonTimeslot.companyFairContactPerson
                        .contactPerson
                const text = `${contactPerson.name || ''}`.trim() || 'Unknown'
                uniqueContactPersons.set(
                    appointment.contactPersonTimeslot?.companyFairContactPerson
                        .id,
                    {
                        id: appointment.contactPersonTimeslot
                            ?.companyFairContactPerson.id,
                        text,
                    }
                )
            }

            if (
                appointment.contactPersonTimeslot?.companyFairContactPerson
                    ?.companyFairParticipation?.company
            ) {
                const company =
                    appointment.contactPersonTimeslot.companyFairContactPerson
                        .companyFairParticipation.company
                const text = company.name || 'Unknown Company'
                uniqueCompanies.set(company.id, { id: company.id, text })
            }

            if (
                appointment.contactPersonTimeslot?.companyFairContactPerson
                    ?.companyFairParticipation?.fair
            ) {
                const fair =
                    appointment.contactPersonTimeslot.companyFairContactPerson
                        .companyFairParticipation.fair
                const text = fair.name || 'Unknown Fair'
                uniqueFairs.set(fair.id, { id: fair.id, text })
            }
        })

        return {
            applicants: Array.from(uniqueApplicants.values()),
            contactPersons: Array.from(uniqueContactPersons.values()),
            companies: Array.from(uniqueCompanies.values()),
            fairs: Array.from(uniqueFairs.values()),
        }
    }

    async notifyAppointmentStatusUpdate(
        createNotificationInput: CreateNotificationInput,
        applicantId?: string,
        chatRoomId?: string,
        appointmentId?: string,
        companyName?: string,
        appointmentStatus?: AppointmentStatus,
        contactPersonName?: string,
        appointmentDateTime?: Date
    ) {
        if (!appointmentStatus) return

        if (appointmentId) {
            let title: string
            let body: string

            const formatDateTime = (date: Date): string => {
                const day = date.getDate().toString().padStart(2, '0')
                const month = (date.getMonth() + 1).toString().padStart(2, '0')
                const hours = date.getHours().toString().padStart(2, '0')
                const minutes = date.getMinutes().toString().padStart(2, '0')
                const year = date.getFullYear().toString()
                return `${day}.${month}.${year}, ${hours}:${minutes}`
            }

            const formattedDateTime = appointmentDateTime
                ? formatDateTime(appointmentDateTime)
                : ''

            switch (appointmentStatus) {
                case AppointmentStatus.REJECTED:
                    title = 'FUTURE.gram Anfrage abgelehnt'
                    body = `Deine Anfrage bei ${contactPersonName} am ${formattedDateTime} wurde leider abgelehnt.`
                    break

                case AppointmentStatus.CONFIRMED:
                    title = 'FUTURE.gram Anfrage angenommen!'
                    body = `Glückwunsch! Deine Gesprächsanfrage bei ${contactPersonName} am ${formattedDateTime} wurde angenommen.`
                    break

                default:
                    title = `Terminanfrage ${appointmentStatus?.toLowerCase()}`
                    body = `Deine Terminanfrage mit ${companyName} wurde beantwortet. Schaue in die App für weitere Details`
                    break
            }

            const params = {
                companyName,
                chatRoomId,
                createNotificationInput,
                createPushNotificationInput: {
                    title,
                    body,
                    actionUrl: '/fair/appointments/' + appointmentId,
                },
                applicantId,
                appointmentId,
                type: 'newAppointmentChat',
            }

            await this.notificationService.sendPushNotification(params)
        } else {
            throw new Error('AppointmentID must be provided.')
        }
    }

    async setAppointmentAsNew(appointmentId: string) {
        const updatedAppointment = await this.prisma.appointment.update({
            where: { id: appointmentId },
            data: { applicantIsNew: true },
            include: this.appointmentIncludes(),
        })

        await this.appointmentPublisherService.publishOnUpdate(
            updatedAppointment,
            updatedAppointment.applicantId,
            updatedAppointment.contactPersonTimeslot?.companyFairContactPerson
                ?.contactPersonId
        )
    }

    async setAppointmentAsOld(appointmentId: string) {
        const updatedAppointment = await this.prisma.appointment.update({
            where: { id: appointmentId },
            data: { applicantIsNew: false },
            include: this.appointmentIncludes(),
        })

        await this.appointmentPublisherService.publishOnUpdate(
            updatedAppointment,
            updatedAppointment.applicantId,
            updatedAppointment.contactPersonTimeslot?.companyFairContactPerson
                ?.contactPersonId
        )

        return updatedAppointment
    }
}
